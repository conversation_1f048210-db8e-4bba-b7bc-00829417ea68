import type { BaseEntity } from './utils';

/**
 * 团队列表项
 */
export interface TeamListItem {
  id: string;
  logoKey: string;
  logoUrl: string;
  name: string;
  isVip: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 团队信息
 */
export interface Team extends BaseEntity {
  /**
   * 账号数量
   */
  accountCount: number;
  /**
   * 账号数量上限
   */
  accountCountLimit: number;
  /**
   * 团队邀请码
   */
  code: string;
  /**
   * VIP 版到期时间, 免费版无此属性值
   */
  expiredAt?: number;
  /**
   * 是否为免费版
   */
  isVip?: boolean;
  /**
   * 团队LOGO图片存储KEY
   */
  logoKey: string;
  /**
   * 团队LOGO图片地址
   */
  logoUrl: string;
  /**
   * 成员数量
   */
  memberCount: number;
  /**
   * 成员数量上限
   */
  memberCountLimit: number;
  /**
   * 团队名称
   */
  name: string;
  /**
   * 总流量
   */
  networkTraffic: number;
  /**
   * 已使用流量
   */
  useNetworkTraffic: number;
}
