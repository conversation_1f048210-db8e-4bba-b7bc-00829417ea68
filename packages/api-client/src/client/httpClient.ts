/**
 * HTTP客户端
 */
import type { AxiosInstance, AxiosRequestConfig, AxiosError } from 'axios';
import axios from 'axios';
import type { ApiConfigOptions } from './config';
import { defaultApiConfig } from './config';
import { setupInterceptors, type ShowErrorNotificationFn } from './interceptors';
import type { ApiResult } from '../types/api';
import { ApiError, ResponseStatus } from '../types/api';

/**
 * HTTP 方法接口定义
 * 定义了 HTTP 客户端支持的所有请求方法
 */
export interface HttpMethods {
  /**
   * GET 请求
   * @param url 请求地址
   * @param params 查询参数
   * @returns 响应数据
   */
  get: <T>(url: string, params?: Record<string, unknown>) => Promise<T>;

  /**
   * POST 请求
   * @param url 请求地址
   * @param data 请求数据
   * @returns 响应数据
   */
  post: <T>(url: string, data?: unknown) => Promise<T>;

  /**
   * PUT 请求
   * @param url 请求地址
   * @param data 请求数据
   * @returns 响应数据
   */
  put: <T>(url: string, data?: Record<string, unknown>) => Promise<T>;

  /**
   * DELETE 请求
   * @param url 请求地址
   * @param params 查询参数
   * @returns 响应数据
   */
  delete: <T>(url: string, params?: Record<string, unknown>) => Promise<T>;

  /**
   * 自定义请求
   * @param config 请求配置
   * @returns 响应数据
   */
  request: <T>(config: AxiosRequestConfig) => Promise<T>;
}

/**
 * HTTP客户端单例类
 * 管理全局唯一的 Axios 实例和配置
 */
export class HttpClientSingleton {
  // 使用私有静态字段存储单例实例
  private static _instance: HttpClientSingleton | null = null;

  // 私有成员变量
  private readonly _axiosInstance: AxiosInstance;
  private _apiConfig: ApiConfigOptions;
  private _showErrorNotification?: ShowErrorNotificationFn;
  private _http: HttpMethods;

  /**
   * 私有构造函数，防止外部直接创建实例
   * @param apiConfig API 配置
   * @param showErrorNotification 显示错误通知的函数
   */
  private constructor(
    apiConfig: ApiConfigOptions = defaultApiConfig,
    showErrorNotification?: ShowErrorNotificationFn
  ) {
    // 初始化配置
    this._apiConfig = { ...apiConfig };
    this._showErrorNotification = showErrorNotification;

    // 创建 Axios 实例
    this._axiosInstance = this.createAxiosInstance(this._apiConfig);

    // 设置拦截器
    this.setupAxiosInterceptors();

    // 初始化 HTTP 方法
    this._http = this.createHttpMethods();
  }

  /**
   * 获取单例实例
   * 如果实例不存在，则创建新实例
   * @param apiConfig API 配置
   * @param showErrorNotification 显示错误通知的函数
   * @returns HttpClientSingleton 单例实例
   */
  public static getInstance(
    apiConfig?: ApiConfigOptions,
    showErrorNotification?: ShowErrorNotificationFn
  ): HttpClientSingleton {
    if (HttpClientSingleton._instance === null) {
      HttpClientSingleton._instance = new HttpClientSingleton(
        apiConfig || defaultApiConfig,
        showErrorNotification
      );
    }
    return HttpClientSingleton._instance;
  }

  /**
   * 创建 Axios 实例
   * @param config API 配置
   * @returns 配置好的 Axios 实例
   */
  private createAxiosInstance(config: ApiConfigOptions): AxiosInstance {
    return axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: { ...config.headers },
    });
  }

  /**
   * 设置 Axios 拦截器
   */
  private setupAxiosInterceptors(): void {
    setupInterceptors(
      this._axiosInstance,
      this._apiConfig,
      this._showErrorNotification
    );
  }

  /**
   * 重新配置客户端
   * @param apiConfig API 配置
   * @param showErrorNotification 显示错误通知的函数
   * @returns 当前实例，支持链式调用
   */
  public configure(
    apiConfig?: Partial<ApiConfigOptions>,
    showErrorNotification?: ShowErrorNotificationFn
  ): HttpClientSingleton {
    let configChanged = false;

    // 更新配置
    if (apiConfig && Object.keys(apiConfig).length > 0) {
      this._apiConfig = { ...this._apiConfig, ...apiConfig };
      configChanged = true;

      // 更新 axios 实例配置
      this.updateAxiosConfig();
    }

    // 更新错误通知函数
    if (showErrorNotification) {
      this._showErrorNotification = showErrorNotification;
      configChanged = true;
    }

    // 只有在配置发生变化时才重新设置拦截器
    if (configChanged) {
      this.resetInterceptors();
    }

    // 返回实例自身，支持链式调用
    return this;
  }

  /**
   * 更新 Axios 实例配置
   */
  private updateAxiosConfig(): void {
    if (this._axiosInstance.defaults) {
      // 更新基本配置
      this._axiosInstance.defaults.baseURL = this._apiConfig.baseUrl;
      this._axiosInstance.defaults.timeout = this._apiConfig.timeout;

      // 更新 headers - 安全地处理 headers
      if (this._axiosInstance.defaults.headers && this._apiConfig.headers) {
        // 更新通用 headers
        Object.entries(this._apiConfig.headers).forEach(([key, value]) => {
          if (this._axiosInstance.defaults.headers.common) {
            this._axiosInstance.defaults.headers.common[key] = value;
          }
        });
      }
    }
  }

  /**
   * 重置拦截器
   */
  private resetInterceptors(): void {
    // 先清除所有拦截器
    this._axiosInstance.interceptors.request.clear();
    this._axiosInstance.interceptors.response.clear();

    // 重新设置拦截器
    this.setupAxiosInterceptors();
  }

  /**
   * 创建 HTTP 方法
   * @returns HTTP 方法对象
   */
  private createHttpMethods(): HttpMethods {
    return {
      get: <T>(url: string, params?: Record<string, unknown>): Promise<T> => {
        return this.baseRequest<T>({ url, method: 'GET', params });
      },

      post: <T>(url: string, data?: unknown): Promise<T> => {
        return this.baseRequest<T>({ url, method: 'POST', data });
      },

      put: <T>(url: string, data?: Record<string, unknown>): Promise<T> => {
        return this.baseRequest<T>({ url, method: 'PUT', data });
      },

      delete: <T>(url: string, params?: Record<string, unknown>): Promise<T> => {
        return this.baseRequest<T>({ url, method: 'DELETE', params });
      },

      request: <T>(config: AxiosRequestConfig): Promise<T> => {
        return this.baseRequest<T>(config);
      },
    };
  }

  /**
   * 发送请求的基础函数
   * @param config 请求配置
   * @returns 响应数据
   */
  private async baseRequest<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this._axiosInstance.request<ApiResult<T>>(config);
      // 成功响应数据已在拦截器中校验，这里直接返回 data 字段
      return response.data.data as T;
    } catch (error) {
      // 错误已在拦截器中处理并显示通知 (或被去抖动阻止)
      if (error instanceof ApiError) {
        throw error; // 直接将拦截器 reject 的 ApiError 抛出
      } else {
        // 增强错误处理
        return this.handleRequestError(error, config);
      }
    }
  }

  /**
   * 处理请求错误
   * @param error 捕获的错误
   * @param config 请求配置
   * @returns 不会返回，总是抛出异常
   */
  private handleRequestError(error: unknown, config: AxiosRequestConfig): never {
    // 记录详细错误信息
    console.error(
      `Request error for ${config.method?.toUpperCase() || 'UNKNOWN'} ${config.url || 'UNKNOWN'}:`,
      error
    );

    // 处理 Axios 错误
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;

      // 根据错误类型创建适当的 ApiError
      if (axiosError.code === 'ECONNABORTED') {
        throw new ApiError(
          ResponseStatus.SERVER_ERROR,
          `请求超时 (${this._apiConfig.timeout}ms)`
        );
      } else if (axiosError.code === 'ERR_NETWORK') {
        throw new ApiError(
          ResponseStatus.SERVER_ERROR,
          '网络连接错误，请检查您的网络连接'
        );
      } else if (axiosError.response) {
        // 有响应但状态码错误
        const status = axiosError.response.status;
        const message = axiosError.response.statusText || '请求失败';
        throw new ApiError(status, message);
      }
    }

    // 未知错误
    throw new ApiError(
      ResponseStatus.SERVER_ERROR,
      '发生未知错误，请稍后重试'
    );
  }

  /**
   * 获取 Axios 实例
   */
  public get axiosInstance(): AxiosInstance {
    return this._axiosInstance;
  }

  /**
   * 获取 HTTP 方法
   */
  public get http(): HttpMethods {
    return this._http;
  }

  /**
   * 获取当前 API 配置（返回副本以防止外部修改）
   */
  public get apiConfig(): ApiConfigOptions {
    return { ...this._apiConfig };
  }

  /**
   * 获取当前错误通知函数
   */
  public get showErrorNotification(): ShowErrorNotificationFn | undefined {
    return this._showErrorNotification;
  }
}

/**
 * 创建 HTTP 客户端
 *
 * 注意：此函数不会创建新的 HTTP 客户端实例，而是配置并返回单例实例的引用。
 * 这样可以确保整个应用中只有一个 Axios 实例，同时保持向后兼容性。
 *
 * @param apiConfig API 配置
 * @param showErrorNotification 显示错误通知的函数
 * @returns HTTP 客户端实例和方法
 */
export const createHttpClient = (
  apiConfig: ApiConfigOptions = defaultApiConfig,
  showErrorNotification?: ShowErrorNotificationFn
): { axiosInstance: AxiosInstance; http: HttpMethods } => {
  // 获取单例实例
  const clientSingleton = HttpClientSingleton.getInstance();

  // 如果提供了新的配置，则重新配置单例
  if (apiConfig !== defaultApiConfig || showErrorNotification) {
    clientSingleton.configure(apiConfig, showErrorNotification);
  }

  return {
    axiosInstance: clientSingleton.axiosInstance,
    http: clientSingleton.http,
  };
};

/**
 * 配置 API 客户端
 *
 * 此函数用于配置全局 API 客户端单例实例。
 * 可以在应用程序的任何地方调用此函数来更新配置或错误通知函数。
 *
 * @param apiConfig API 配置（部分配置）
 * @param showErrorNotification 显示错误通知的函数
 * @returns 配置后的单例实例，支持链式调用
 */
export const configureApiClient = (
  apiConfig?: Partial<ApiConfigOptions>,
  showErrorNotification?: ShowErrorNotificationFn
): HttpClientSingleton => {
  return HttpClientSingleton.getInstance().configure(apiConfig, showErrorNotification);
};

/**
 * 获取 API 客户端配置
 *
 * 此函数用于获取当前 API 客户端的配置。
 * 返回的配置是一个副本，修改它不会影响实际配置。
 *
 * @returns 当前 API 配置的副本
 */
export const getApiClientConfig = (): ApiConfigOptions => {
  return HttpClientSingleton.getInstance().apiConfig;
};

// 获取单例实例
const clientSingleton = HttpClientSingleton.getInstance();

// 导出默认实例和方法（保持向后兼容性）
export const httpClient = clientSingleton.axiosInstance;
export const http = clientSingleton.http;
