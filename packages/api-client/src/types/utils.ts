/**
 * 类型工具函数和通用类型定义
 */

/**
 * 基础类型工具
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type PickRequired<T, K extends keyof T> = T & { [P in K]-?: T[P] };

/**
 * 状态枚举类型
 */
export type Status = 'pending' | 'delivering' | 'successful' | 'failed' | 'auditing' | 'rejected' | 'approved';

/**
 * 基础实体接口
 */
export interface BaseEntity {
  id: string;
  createdAt: number;
}

/**
 * 平台相关类型
 */
export interface PlatformBase {
  name: string;
  key: string;
  icon?: string;
}

/**
 * 媒体内容统计类型
 */
export interface MediaStatistics {
  viewCount: number;
  likeCount: number;
  commentCount: number;
  shareCount: number;
  collectCount: number;
}

/**
 * 类型守卫函数
 */
export const isStatus = (value: string): value is Status => {
  return ['pending', 'delivering', 'successful', 'failed', 'auditing', 'rejected', 'approved'].includes(value);
};

/**
 * 类型转换工具
 */
export const toStatus = (value: string): Status => {
  return isStatus(value) ? value : 'pending';
};
