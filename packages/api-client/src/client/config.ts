/**
 * API配置
 */

/**
 * 全局 API 配置选项
 */
export interface ApiConfigOptions {
  /** API 基础 URL */
  baseUrl: string;
  /** 成功响应状态码 */
  responseSuccess: number;
  /** 无效 token 状态码 */
  invalidToken: number;
  /** 请求超时时间（毫秒） */
  timeout: number;
  /** 请求头 */
  headers: Record<string, string>;
  /** 错误通知去抖动时间（毫秒） */
  notificationDebounceMs: number;
  /** 是否自动处理 token 过期 */
  autoHandleTokenExpiration: boolean;
  /** 是否启用请求重试 */
  enableRetry: boolean;
  /** 最大重试次数 */
  maxRetryCount: number;
  /** 重试延迟（毫秒） */
  retryDelay: number;
}

/**
 * 创建 API 配置
 * @param options 部分配置选项
 * @returns 完整的 API 配置
 */
export const createApiConfig = (options: Partial<ApiConfigOptions> = {}): ApiConfigOptions => {
  return {
    baseUrl: options.baseUrl || '',
    responseSuccess: options.responseSuccess ?? 0,
    invalidToken: options.invalidToken ?? 401,
    timeout: options.timeout ?? 50000,
    headers: options.headers || {
      'Content-Type': 'application/json',
    },
    notificationDebounceMs: options.notificationDebounceMs ?? 5000,
    autoHandleTokenExpiration: options.autoHandleTokenExpiration ?? true,
    enableRetry: options.enableRetry ?? false,
    maxRetryCount: options.maxRetryCount ?? 3,
    retryDelay: options.retryDelay ?? 1000,
  };
};

// 默认配置
export const defaultApiConfig = createApiConfig();
