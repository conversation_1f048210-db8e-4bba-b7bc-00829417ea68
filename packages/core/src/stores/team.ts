import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { useShallow } from "zustand/react/shallow";
import type { User } from "@yixiaoer/api-client";

interface UserStore {
  user: User;
  setUser: (user: User) => void;
}

export function useUserStoreSelect<U>(selector: (state: UserStore) => U) {
  return useUserStore(useShallow(selector));
}

export const useUserStore = create<UserStore>()(
  devtools(
    (set) => ({
      user: {} as User,
      setUser: (user: User) => set({ user }),
    }),
    {
      name: "UserStore",
    }
  )
);
