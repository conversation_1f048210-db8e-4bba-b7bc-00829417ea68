import { authorize, sendVerifyCode, getUserInfo } from "@yixiaoer/api-client";
import { createMutationHook, createQueryHook } from "../utils/query";
/**
 * 发送验证码的 hook
 */
export const useSendVerifyCodeMutation = createMutationHook(
  (params: { phone: string; sence: string }) => sendVerifyCode(params)
);

/**
 * 用户登录的 hook
 */
export const useAuthorizeMutation = createMutationHook(
  async ({ phone, code }: { phone: string; code: string }) =>
    authorize({ phone, code })
);

/**
 * 获取用户信息的 hook
 */
export const useUserInfoQuery = createQueryHook(["userInfo"], getUserInfo);
