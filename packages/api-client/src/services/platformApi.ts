/**
 * 平台相关API
 *
 * 提供与平台内容交互的API接口，包括获取音乐列表、音乐分类等功能。
 * 这些API用于支持跨平台内容发布和管理。
 *
 * @module platformApi
 */
import { http } from "../client";

/**
 * 平台音乐数据项
 *
 * 表示平台音乐的基本信息，包含音乐ID、名称、作者、时长和封面URL等属性。
 * 不同平台可能会有额外的特定属性，通过索引签名支持扩展。
 *
 * @interface MusicPlatformDataItem
 * @property {string} id - 音乐唯一标识符
 * @property {string} name - 音乐名称
 * @property {string} author - 音乐作者/艺术家
 * @property {number} duration - 音乐时长（秒）
 * @property {string} coverUrl - 音乐封面图片URL
 * @property {unknown} [key: string] - 平台特定的其他属性
 */
export interface MusicPlatformDataItem {
  yixiaoerId: string;
  yixiaoerName: string;
  authorName: string;
  playUrl: string;
  duration: number;
  raw: unknown;
}

/**
 * 获取平台账号的音乐列表
 *
 * 根据查询参数获取指定平台账号的音乐列表，支持关键词搜索、分类筛选和分页。
 *
 * @param {string} platformAccountId - 平台账号ID
 * @param {Object} query - 查询参数对象
 * @param {string} [query.keyWord] - 搜索关键词
 * @param {string} [query.nextPage] - 下一页标识符，用于分页加载
 * @param {string} [query.categoryId] - 音乐分类ID
 * @param {string} [query.categoryName] - 音乐分类名称
 * @returns {Promise<{dataList?: MusicPlatformDataItem[]; nextPage?: string | null}>} 包含音乐列表和分页信息的对象
 *
 * @example
 * // 搜索音乐
 * const result = await getMusicList('account-123', { keyWord: '流行' });
 *
 * // 按分类获取音乐
 * const result = await getMusicList('account-123', { categoryId: 'category-456' });
 *
 * // 加载下一页
 * if (result.nextPage) {
 *   const nextPageResult = await getMusicList('account-123', { nextPage: result.nextPage });
 * }
 */
export const getMusicList = (
  platformAccountId: string,
  query: {
    keyWord?: string;
    nextPage?: string;
    categoryId?: string;
    categoryName?: string;
  }
) => {
  // 值为空时，不添加到url中
  const queryParams = Object.fromEntries(
    Object.entries(query).filter(([, value]) => value !== undefined)
  );
  return http.get<{
    dataList?: MusicPlatformDataItem[];
    nextPage?: string | null;
  }>(`/platform-accounts/${platformAccountId}/music`, queryParams);
};

/**
 * 获取平台账号的音乐分类列表
 *
 * 获取指定平台账号可用的音乐分类，返回分类列表数据。
 *
 * @param {string} platformAccountId - 平台账号ID
 * @returns {Promise<{dataList: Array<{yixiaoerId: string; yixiaoerName: string; raw: unknown}>}>} 包含音乐分类列表的对象
 *
 * @example
 * // 获取指定平台账号的音乐分类
 * const musicCategories = await getMusicCategory('account-123');
 * // 使用分类ID获取音乐列表
 * const musicList = await getMusicList('account-123', { categoryId: musicCategories.dataList[0].yixiaoerId });
 *
 * @see GET /platform-accounts/{platformAccountId}/music/category
 * @see https://app.apifox.com/link/project/4626224/apis/api-*********
 */
export const getMusicCategory = (platformAccountId: string) => {
  return http.get<{
    dataList: { yixiaoerId: string; yixiaoerName: string; raw: unknown }[];
  }>(`/platform-accounts/${platformAccountId}/music/category`);
};
