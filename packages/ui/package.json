{"name": "@yixiaoer/ui", "version": "0.1.0", "description": "共享UI组件库", "exports": {"./*": "./src/components/ui/*.tsx", "./custom/*": "./src/components/*.tsx", "./lib/*": "./src/lib/*.ts", "./hook": "./src/hooks/index.ts"}, "scripts": {"typecheck": "tsc --noEmit", "check-types": "tsc --noEmit", "ui-add": "pnpm dlx shadcn@2.3.0 add"}, "dependencies": {"@hookform/resolvers": "catalog:", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "catalog:", "@radix-ui/react-aspect-ratio": "catalog:", "@radix-ui/react-avatar": "catalog:", "@radix-ui/react-checkbox": "catalog:", "@radix-ui/react-collapsible": "1.1.1", "@radix-ui/react-context-menu": "2.2.2", "@radix-ui/react-dialog": "catalog:", "@radix-ui/react-dropdown-menu": "catalog:", "@radix-ui/react-hover-card": "1.1.1", "@radix-ui/react-label": "catalog:", "@radix-ui/react-menubar": "1.1.6", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-popover": "1.1.1", "@radix-ui/react-progress": "catalog:", "@radix-ui/react-radio-group": "1.2.0", "@radix-ui/react-scroll-area": "catalog:", "@radix-ui/react-select": "2.1.1", "@radix-ui/react-separator": "catalog:", "@radix-ui/react-slider": "1.2.0", "@radix-ui/react-slot": "catalog:", "@radix-ui/react-switch": "catalog:", "@radix-ui/react-tabs": "catalog:", "@radix-ui/react-toast": "1.2.1", "@radix-ui/react-toggle": "1.1.0", "@radix-ui/react-toggle-group": "1.1.0", "@radix-ui/react-tooltip": "1.1.2", "@radix-ui/react-use-controllable-state": "^1.2.2", "@radix-ui/react-visually-hidden": "catalog:", "class-variance-authority": "catalog:", "clsx": "catalog:", "cmdk": "1.0.0", "date-fns": "catalog:", "embla-carousel-react": "catalog:", "input-otp": "catalog:", "lucide-react": "catalog:", "next-themes": "catalog:", "react-day-picker": "8.10.1", "react-hook-form": "catalog:", "react-resizable-panels": "2.1.6", "recharts": "2.15.3", "sonner": "catalog:", "tailwind-merge": "catalog:", "tailwindcss-animate": "catalog:", "vaul": "catalog:", "zod": "catalog:"}, "devDependencies": {"@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@yixiaoer/eslint-config": "workspace:*", "autoprefixer": "catalog:", "postcss": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}}