# API 客户端全局配置使用指南

本文档介绍如何使用 API 客户端的全局配置功能，包括配置选项、自定义 token 获取和刷新逻辑等。

## 基本用法

### 设置全局配置

在应用程序初始化时，可以使用 `configureApiClient` 函数设置全局配置：

```typescript
import { configureApiClient } from '@yixiaoer/api-client';

// 设置全局配置
configureApiClient({
  apiConfig: {
    baseUrl: 'https://api.example.com',
    timeout: 10000,
    autoHandleTokenExpiration: true
  }
});
```

### 获取当前全局配置

可以使用 `getApiClientConfig` 函数获取当前的全局配置：

```typescript
import { getApiClientConfig } from '@yixiaoer/api-client';

const currentConfig = getApiClientConfig();
console.log('当前 API 基础 URL:', currentConfig?.apiConfig?.baseUrl);
```

## 自定义 Token 获取和刷新

### 基本示例

```typescript
import { configureApiClient } from '@yixiaoer/api-client';

configureApiClient({
  tokenGetter: {
    getToken: () => localStorage.getItem('custom_token') || '',
    refreshToken: async () => {
      // 实现自定义的 token 刷新逻辑
      const response = await fetch('https://api.example.com/refresh-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: localStorage.getItem('refresh_token') })
      });
      const data = await response.json();
      if (data.token) {
        localStorage.setItem('custom_token', data.token);
        return data.token;
      }
      return '';
    },
    setToken: (token) => localStorage.setItem('custom_token', token),
    logout: () => {
      localStorage.removeItem('custom_token');
      localStorage.removeItem('refresh_token');
      // 可以添加重定向逻辑
      window.location.href = '/login';
    }
  }
});
```

### 使用 React Context 管理 Token

```typescript
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { configureApiClient, TokenGetter } from '@yixiaoer/api-client';

// 创建 Auth Context
const AuthContext = createContext<{
  token: string;
  setToken: (token: string) => void;
  logout: () => void;
}>({
  token: '',
  setToken: () => {},
  logout: () => {}
});

// 创建 Auth Provider
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [token, setToken] = useState('');

  // 创建 Token 获取器
  const tokenGetter: TokenGetter = {
    getToken: () => token,
    refreshToken: async () => {
      // 实现 token 刷新逻辑
      try {
        const response = await fetch('https://api.example.com/refresh-token', {
          headers: { Authorization: token }
        });
        const data = await response.json();
        if (data.token) {
          setToken(data.token);
          return data.token;
        }
      } catch (error) {
        console.error('Token refresh failed:', error);
      }
      return '';
    },
    logout: () => setToken('')
  };

  // 当 token 变化时，更新 API 客户端配置
  useEffect(() => {
    configureApiClient({
      tokenGetter
    });
  }, [token]);

  const logout = () => {
    setToken('');
    // 可以添加其他登出逻辑
  };

  return (
    <AuthContext.Provider value={{ token, setToken, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

// 使用 Auth Context 的 Hook
export const useAuth = () => useContext(AuthContext);
```

## 自定义错误处理

```typescript
import { configureApiClient } from '@yixiaoer/api-client';
import { notification } from 'antd'; // 使用 Ant Design 的通知组件

configureApiClient({
  showErrorNotification: (title, text, status) => {
    notification.error({
      message: title,
      description: text,
      duration: 3
    });
    
    // 可以添加自定义错误处理逻辑
    if (status === 500) {
      // 记录服务器错误
      console.error(`Server error: ${text}`);
      // 可以发送到错误监控系统
    }
  }
});
```

## 完整配置示例

```typescript
import { configureApiClient } from '@yixiaoer/api-client';

configureApiClient({
  // API 配置
  apiConfig: {
    baseUrl: 'https://api.example.com',
    timeout: 15000,
    responseSuccess: 0,
    invalidToken: 401,
    headers: {
      'Content-Type': 'application/json',
      'X-App-Version': '1.0.0'
    },
    notificationDebounceMs: 3000,
    autoHandleTokenExpiration: true,
    enableRetry: true,
    maxRetryCount: 3,
    retryDelay: 1000
  },
  
  // Token 获取器
  tokenGetter: {
    getToken: () => localStorage.getItem('auth_token') || '',
    refreshToken: async () => {
      // 实现 token 刷新逻辑
      return ''; // 返回新 token
    },
    setToken: (token) => localStorage.setItem('auth_token', token),
    logout: () => {
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
  },
  
  // 错误通知函数
  showErrorNotification: (title, text, status) => {
    console.error(`[${title}] ${text}`, status);
    // 使用您的通知库显示错误
  },
  
  // 请求重试配置
  retry: {
    enable: true,
    maxCount: 3,
    delay: 1000
  }
});
```

## 在 Services 层中使用

API 客户端的 services 层会自动使用全局配置，无需额外修改：

```typescript
import { userApi } from '@yixiaoer/api-client';

// 这些方法会自动使用全局配置
async function getUserData() {
  try {
    const user = await userApi.getUserInfo();
    return user;
  } catch (error) {
    console.error('Failed to get user info:', error);
    throw error;
  }
}
```

## 创建自定义 HTTP 客户端

如果需要创建具有不同配置的 HTTP 客户端实例，可以使用 `createHttpClient` 函数：

```typescript
import { createHttpClient } from '@yixiaoer/api-client';

// 创建自定义 HTTP 客户端
const { http, axiosInstance } = createHttpClient({
  apiConfig: {
    baseUrl: 'https://api.other-service.com',
    timeout: 30000
  }
});

// 使用自定义 HTTP 客户端
async function fetchData() {
  try {
    const result = await http.get('/data');
    return result;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}
```

自定义 HTTP 客户端会继承全局配置，但可以覆盖特定选项。
