import { LoaderCircle } from 'lucide-react'
import { But<PERSON> } from './ui/button'

type Props = {
  isPending?: boolean
} & React.ComponentProps<typeof Button>

export const LoadingButton = ({ children, isPending, ...props }: Props) => (
  <Button translate="no" {...props}>
    {isPending && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
    <span translate="no">{children}</span>
  </Button>
)
