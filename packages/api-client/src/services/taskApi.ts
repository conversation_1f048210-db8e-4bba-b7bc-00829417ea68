/**
 * 任务相关API
 */
import { http } from '../client';
import type { TaskWrapper, CreateTaskRequest, Task, PushingTaskSetResponse, PaginatedResult } from '../types';

/**
 * 获取任务列表
 * @param page 页码
 * @param size 每页条数
 * @returns 任务列表
 */
export const getTaskList = (page = 1, size = 10) => {
  return http.get<PaginatedResult<TaskWrapper>>('/taskSets', { page, size });
};

/**
 * 获取移动端分发任务列表
 * @param taskSetId 任务集ID
 * @returns 任务列表
 */
export const getChildTaskList = (taskSetId: string) => {
  return http.get<Task[]>(`/taskSets/${taskSetId}/tasks`);
};

/**
 * 获取任务集详情
 * @param taskSetId 任务集ID
 * @returns 任务集详情
 */
export const getTaskSet = (taskSetId: string) => {
  return http.get<PushingTaskSetResponse>(`/taskSets/${taskSetId}`);
};

/**
 * 创建任务
 * @param data 任务数据
 * @returns 任务ID
 */
export const createTask = (data: CreateTaskRequest) => {
  return http.post<string>('/taskSets', data);
};
