import type { BaseEntity } from './utils';

/**
 * 验证码请求
 */
export interface VerifyCodeRequest {
  phone: string;
  sence: string;
}

/**
 * 授权请求
 */
export interface AuthRequest {
  phone: string;
  code: string;
}

/**
 * 用户信息
 */
export interface User extends BaseEntity {
  avatarKey: string;
  avatarUrl: string;
  latestTeamId: string;
  nickName: string;
  phone: string;
}

/**
 * 认证信息
 */
export interface Auth {
  authorization: string;
}
