/**
 * 授权管理工具
 */

// 本地存储键
const TOKEN_KEY = 'token';

/**
 * 设置令牌
 * @param token 授权令牌
 */
export const setToken = (token: string): void => {
  if (typeof localStorage !== 'undefined') {
    localStorage.setItem(TOKEN_KEY, token);
  }
};

/**
 * 获取令牌
 * @returns 令牌字符串
 */
export const getToken = (): string => {
  if (typeof localStorage !== 'undefined') {
    return localStorage.getItem(TOKEN_KEY) || '';
  }
  return '';
};

/**
 * 退出登录
 * 注意：此函数不包含重定向逻辑，需要在调用处处理
 */
export const logout = (): void => {
  if (typeof localStorage !== 'undefined') {
    localStorage.removeItem(TOKEN_KEY);
  }
};
