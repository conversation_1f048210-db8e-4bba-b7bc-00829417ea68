# @yixiaoer/ui

易小二共享UI组件库，为app和desktop应用提供统一的UI组件。

## 特性

- 基于shadcn/ui组件系统
- 支持按需导入
- 提供统一的设计语言
- 适配移动端和桌面端

## 安装

```bash
# 在项目中安装
pnpm add @yixiaoer/ui
```

## 使用

```tsx
import { Button } from '@yixiaoer/ui';

export default function MyComponent() {
  return (
    <Button variant="default">
      点击我
    </Button>
  );
}
```

## 开发

```bash
# 开发模式
pnpm dev

# 构建
pnpm build

# 类型检查
pnpm typecheck
```

## 组件分类

- 基础UI组件 - 按钮、输入框等基础组件
- 布局组件 - 用于页面布局的组件
- 数据展示组件 - 用于展示数据的组件
- 导航组件 - 导航相关组件
- 反馈组件 - 提供用户反馈的组件
- 编辑器组件 - 富文本编辑器等
- 账户相关组件 - 与账户相关的组件
- 复合组件 - 由多个基础组件组合而成的复杂组件
