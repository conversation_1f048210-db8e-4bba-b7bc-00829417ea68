{"name": "@yixiaoer/api-client", "version": "1.0.0", "description": "API client library for Yixiaoer applications", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint .", "check-types": "tsc --noEmit"}, "dependencies": {"axios": "catalog:", "lodash": "catalog:"}, "devDependencies": {"@types/lodash": "^4.14.202", "tsup": "^8.5.0", "typescript": "catalog:"}, "peerDependencies": {"sonner": "^1.4.0"}, "publishConfig": {"access": "public"}}