{"name": "@yixiaoer/core", "version": "1.0.0", "description": "Core business logic library for Yixiaoer applications", "exports": {"./utils": "./src/utils/index.ts", "./constants/*": "./src/constants/*.ts", "./types/*": "./src/types/*.ts", "./hooks/*": "./src/hooks/*.ts", "./stores/*": "./src/stores/*.ts", "./components/*": "./src/components/*.tsx"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint .", "check-types": "tsc --noEmit"}, "dependencies": {"zustand": "catalog:", "@tanstack/react-query": "catalog:", "@yixiaoer/api-client": "workspace:*", "@yixiaoer/ui": "workspace:*", "lodash": "catalog:", "zod": "catalog:", "tailwind-merge": "catalog:", "date-fns": "catalog:"}, "devDependencies": {"@yixiaoer/eslint-config": "workspace:*", "@types/lodash": "^4.14.202"}, "peerDependencies": {"react": "catalog:", "react-dom": "catalog:"}, "publishConfig": {"access": "public"}}