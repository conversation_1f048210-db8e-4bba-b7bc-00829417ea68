/**
 * 日期时间工具
 */

import type { Duration } from 'date-fns'
import {
  format,
  parseISO,
  isValid,
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  differenceInMonths,
  differenceInYears,
  addDays,
  addMonths,
  addYears,
  isBefore,
  isAfter,
  startOfDay,
  endOfDay,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  add,
  isThisYear,
  isToday,
} from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { TimeUnit, TimeConversion, DateRange } from './types'

export type { TimeUnit, TimeConversion, DateRange }

export class DateUtils {
  /**
   * 将日期格式化为指定格式的字符串
   * @param date 日期
   * @param formatStr 格式字符串，默认 'yyyy-MM-dd HH:mm:ss'
   */
  static formatDate(
    date: Date | string | number,
    formatStr: string = 'yyyy-MM-dd HH:mm:ss',
  ): string {
    const parsedDate = this.parseDate(date)
    if (!parsedDate) return ''
    return format(parsedDate, formatStr, { locale: zhCN })
  }

  /**
   * 解析日期
   * @param date 日期输入
   */
  static parseDate(date: Date | string | number): Date | null {
    if (date instanceof Date) {
      return isValid(date) ? date : null
    }

    if (typeof date === 'string') {
      const parsed = parseISO(date)
      return isValid(parsed) ? parsed : null
    }

    if (typeof date === 'number') {
      const parsed = new Date(date)
      return isValid(parsed) ? parsed : null
    }

    return null
  }

  /**
   * 获取相对时间描述
   * @param date 日期
   */
  static getRelativeTime(date: Date | string | number): string {
    const parsedDate = this.parseDate(date)
    if (!parsedDate) return ''

    if (isToday(parsedDate)) {
      return '今天'
    }

    if (isThisYear(parsedDate)) {
      return format(parsedDate, 'MM-dd', { locale: zhCN })
    }

    return format(parsedDate, 'yyyy-MM-dd', { locale: zhCN })
  }

  /**
   * 计算两个日期之间的差异
   */
  static differences = {
    inDays: (date1: Date | string | number, date2: Date | string | number): number => {
      const parsed1 = this.parseDate(date1)
      const parsed2 = this.parseDate(date2)
      if (!parsed1 || !parsed2) return 0
      return differenceInDays(parsed1, parsed2)
    },

    inHours: (date1: Date | string | number, date2: Date | string | number): number => {
      const parsed1 = this.parseDate(date1)
      const parsed2 = this.parseDate(date2)
      if (!parsed1 || !parsed2) return 0
      return differenceInHours(parsed1, parsed2)
    },

    inMinutes: (date1: Date | string | number, date2: Date | string | number): number => {
      const parsed1 = this.parseDate(date1)
      const parsed2 = this.parseDate(date2)
      if (!parsed1 || !parsed2) return 0
      return differenceInMinutes(parsed1, parsed2)
    },

    inMonths: (date1: Date | string | number, date2: Date | string | number): number => {
      const parsed1 = this.parseDate(date1)
      const parsed2 = this.parseDate(date2)
      if (!parsed1 || !parsed2) return 0
      return differenceInMonths(parsed1, parsed2)
    },

    inYears: (date1: Date | string | number, date2: Date | string | number): number => {
      const parsed1 = this.parseDate(date1)
      const parsed2 = this.parseDate(date2)
      if (!parsed1 || !parsed2) return 0
      return differenceInYears(parsed1, parsed2)
    },
  }

  /**
   * 日期比较
   */
  static comparisons = {
    isBefore: (date1: Date | string | number, date2: Date | string | number): boolean => {
      const parsed1 = this.parseDate(date1)
      const parsed2 = this.parseDate(date2)
      if (!parsed1 || !parsed2) return false
      return isBefore(parsed1, parsed2)
    },

    isAfter: (date1: Date | string | number, date2: Date | string | number): boolean => {
      const parsed1 = this.parseDate(date1)
      const parsed2 = this.parseDate(date2)
      if (!parsed1 || !parsed2) return false
      return isAfter(parsed1, parsed2)
    },
  }

  /**
   * 日期范围操作
   */
  static dateRanges = {
    getDayRange: (date: Date | string | number): DateRange | null => {
      const parsedDate = this.parseDate(date)
      if (!parsedDate) return null
      return {
        start: startOfDay(parsedDate),
        end: endOfDay(parsedDate),
      }
    },

    getMonthRange: (date: Date | string | number): DateRange | null => {
      const parsedDate = this.parseDate(date)
      if (!parsedDate) return null
      return {
        start: startOfMonth(parsedDate),
        end: endOfMonth(parsedDate),
      }
    },

    getYearRange: (date: Date | string | number): DateRange | null => {
      const parsedDate = this.parseDate(date)
      if (!parsedDate) return null
      return {
        start: startOfYear(parsedDate),
        end: endOfYear(parsedDate),
      }
    },
  }

  /**
   * 日期计算
   */
  static calculations = {
    addDays: (date: Date | string | number, amount: number): Date | null => {
      const parsedDate = this.parseDate(date)
      if (!parsedDate) return null
      return addDays(parsedDate, amount)
    },

    addMonths: (date: Date | string | number, amount: number): Date | null => {
      const parsedDate = this.parseDate(date)
      if (!parsedDate) return null
      return addMonths(parsedDate, amount)
    },

    addYears: (date: Date | string | number, amount: number): Date | null => {
      const parsedDate = this.parseDate(date)
      if (!parsedDate) return null
      return addYears(parsedDate, amount)
    },

    addDuration: (
      duration: number,
      unit: keyof Duration,
      date: string | number | Date = new Date(),
    ) => {
      return add(date, {
        [unit]: duration,
      }).getTime()
    },
  }

  /**
   * 时长转换
   */
  static duration = {
    /**
     * 将秒数转换为时分秒格式
     * @param seconds 秒数
     * @param format 输出格式：'full' 完整格式(00:00:00)，'auto' 自动格式(智能判断是否显示小时)，'minimal' 精简格式(只显示分和秒)
     */
    formatSeconds(seconds: number, format: 'full' | 'auto' | 'minimal' = 'auto'): string {
      if (seconds < 0 || !Number.isFinite(seconds)) return '00:00'

      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const remainingSeconds = Math.floor(seconds % 60)

      const padZero = (num: number): string => String(num).padStart(2, '0')

      switch (format) {
        case 'full':
          return `${padZero(hours)}:${padZero(minutes)}:${padZero(remainingSeconds)}`
        case 'minimal':
          return `${padZero(minutes)}:${padZero(remainingSeconds)}`
        case 'auto':
        default:
          return hours > 0
            ? `${padZero(hours)}:${padZero(minutes)}:${padZero(remainingSeconds)}`
            : `${padZero(minutes)}:${padZero(remainingSeconds)}`
      }
    },

    /**
     * 将秒数转换为中文描述
     * @param seconds 秒数
     * @param short 是否使用简短格式
     */
    formatSecondsInChinese(seconds: number, short: boolean = false): string {
      if (seconds < 0 || !Number.isFinite(seconds)) return '0秒'

      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const remainingSeconds = Math.floor(seconds % 60)

      const parts: string[] = []

      if (hours > 0) {
        parts.push(`${hours}${short ? '时' : '小时'}`)
      }
      if (minutes > 0) {
        parts.push(`${minutes}${short ? '分' : '分钟'}`)
      }
      if (remainingSeconds > 0 || parts.length === 0) {
        parts.push(`${remainingSeconds}${short ? '秒' : '秒钟'}`)
      }

      return parts.join(short ? '' : '')
    },

    /**
     * 将时间段转换为最适合的单位表示
     * - 小于60秒时，以秒为单位
     * - 60秒到60分钟之间，以分钟为单位
     * - 大于60分钟时，以小时为单位
     * 数值最多保留两位小数
     *
     * @param seconds - 需要转换的秒数
     * @returns {TimeConversion} 包含转换后的数值和单位的对象
     */
    convertTime(seconds: number): TimeConversion {
      // 小于60秒
      if (seconds < 60) {
        return {
          value: Number(seconds.toFixed(2)),
          unit: 'second',
          unitName: '秒',
        }
      }

      // 转换为分钟
      const minutes = seconds / 60

      // 小于60分钟
      if (minutes < 60) {
        return {
          value: Number(minutes.toFixed(2)),
          unit: 'minute',
          unitName: '分钟',
        }
      }

      // 转换为小时
      const hours = minutes / 60
      return {
        value: Number(hours.toFixed(2)),
        unit: 'hour',
        unitName: '小时',
      }
    },
  }
}
