# @yixiaoer/api-client

API 客户端库，用于与蚁小二后端 API 进行交互。

## 安装

```bash
pnpm add @yixiaoer/api-client
```

## 使用方法

### 基本用法

```typescript
import { http, getUserInfo, getTeamDetail } from '@yixiaoer/api-client';

// 获取用户信息
const user = await getUserInfo();

// 获取团队详情
const team = await getTeamDetail(user.latestTeamId);
```

### 自定义配置

```typescript
import { configureApiClient, createApiConfig } from '@yixiaoer/api-client';

// 创建自定义配置
const apiConfig = createApiConfig({
  baseUrl: 'https://api.example.com',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'x-client': 'custom-client',
    'x-version': '1.0.0',
  },
});

// 配置全局 API 客户端（单例模式）
configureApiClient(
  apiConfig,
  (title, text, status) => {
    // 自定义错误通知处理
    console.error(`[${title}] ${text}`, status);
  }
);

// 使用配置后的客户端
import { http } from '@yixiaoer/api-client';
const data = await http.get('/some-endpoint');
```

### 使用 createHttpClient（向后兼容）

```typescript
import { createHttpClient, createApiConfig } from '@yixiaoer/api-client';

// 创建自定义配置
const apiConfig = createApiConfig({
  baseUrl: 'https://api.example.com',
  timeout: 10000,
});

// 使用 createHttpClient 配置单例并获取引用
const { http } = createHttpClient({
  apiConfig,
  showErrorNotification: (title, text, status) => {
    // 自定义错误通知处理
    console.error(`[${title}] ${text}`, status);
  }
});

// 使用配置后的客户端
const data = await http.get('/some-endpoint');
```

### 单例模式和错误通知配置

API 客户端现在使用单例模式实现，确保整个应用中只有一个 Axios 实例。

```typescript
import { HttpClientSingleton } from '@yixiaoer/api-client';
import { toast } from 'sonner'; // 或其他通知库

// 获取单例实例
const clientSingleton = HttpClientSingleton.getInstance();

// 配置错误通知
clientSingleton.configure(
  undefined, // 不修改 API 配置
  (title, text, status) => {
    // 使用 toast 通知库显示错误
    toast.error(`${title}: ${text}`);

    // 可以添加额外的错误处理逻辑
    if (status === 500) {
      console.error('服务器错误:', text);
      // 可以发送到错误监控系统
    }
  }
);

// 使用配置后的客户端
import { http } from '@yixiaoer/api-client';
const data = await http.get('/some-endpoint');
```

更多详细信息，请参阅 [单例模式使用指南](./docs/singleton-usage.md)。

### 自定义 Token 获取方法

```typescript
import { createHttpClient, TokenGetter } from '@yixiaoer/api-client';

// 实现自定义 Token 获取器
class CustomTokenGetter implements TokenGetter {
  // 可以从任何来源获取 token，例如 Cookie、Session Storage、Redux 状态等
  async getToken(): Promise<string> {
    // 示例：从 Cookie 获取 token
    return document.cookie
      .split('; ')
      .find(row => row.startsWith('auth_token='))
      ?.split('=')[1] || '';

    // 或者从异步 API 获取 token
    // const response = await fetch('/api/refresh-token');
    // const data = await response.json();
    // return data.token;
  }
}

// 创建使用自定义 Token 获取器的 HTTP 客户端
const { http } = createHttpClient({
  tokenGetter: new CustomTokenGetter()
});

// 使用自定义客户端
const data = await http.get('/some-endpoint');
```

## API 文档

### HTTP 客户端

- `http.get<T>(url, params?)`: 发送 GET 请求
- `http.post<T>(url, data?)`: 发送 POST 请求
- `http.put<T>(url, data?)`: 发送 PUT 请求
- `http.delete<T>(url, params?)`: 发送 DELETE 请求
- `http.request<T>(config)`: 发送自定义请求

### 全局配置

- `configureApiClient(apiConfig?, showErrorNotification?)`: 配置全局 API 客户端
- `HttpClientSingleton.getInstance()`: 获取 HTTP 客户端单例实例
- `HttpClientSingleton.configure(apiConfig?, showErrorNotification?)`: 配置单例实例

### 认证管理

#### 函数式 API（向后兼容）

- `setToken(token)`: 设置认证令牌
- `getToken()`: 获取认证令牌
- `checkLogin()`: 检查是否已登录
- `loginSuccess(token)`: 处理登录成功
- `logout()`: 退出登录

#### 自定义 Token 获取器

- `TokenGetter` 接口: 自定义 token 获取方法的接口
- `DefaultTokenGetter` 类: 默认的 token 获取器实现
- `defaultTokenGetter`: 默认的 token 获取器实例

### API 服务

- 用户 API: `getUserInfo()`, `sendVerifyCode()`, `authorize()`, `getOnlineCount()`
- 团队 API: `getTeamDetail()`, `getTeamList()`, `authTeam()`
- 任务 API: `getTaskList()`, `getChildTaskList()`, `getTaskSet()`, `createTask()`
- 账号 API: `getAccountList()`, `getGroupList()`
- 文件 API: `getUploadUrl()`, `getAccessUrl()`, `getHeadUrl()`, `uploadFile()`
- 平台 API: `getMusicList()`
- 版本 API: `getLatestVersion()`

## 类型定义

该库提供了完整的 TypeScript 类型定义，包括：

- API 响应类型
- 请求参数类型
- 实体类型（用户、团队、任务等）

## 开发

```bash
# 安装依赖
pnpm install

# 构建库
pnpm build

# 开发模式（监视文件变化）
pnpm dev
```
