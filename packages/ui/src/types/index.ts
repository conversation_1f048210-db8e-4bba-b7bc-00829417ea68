/**
 * 通用组件属性类型
 */
export type BaseProps = {
  className?: string;
  children?: React.ReactNode;
};

/**
 * 尺寸变体
 */
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

/**
 * 颜色变体
 */
export type ColorVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'accent'
  | 'destructive'
  | 'muted'
  | 'success'
  | 'warning'
  | 'info';

/**
 * 位置变体
 */
export type Position = 'top' | 'right' | 'bottom' | 'left';

/**
 * 对齐方式
 */
export type Alignment = 'start' | 'center' | 'end';
