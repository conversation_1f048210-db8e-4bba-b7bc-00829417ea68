{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "declaration": true, "declarationDir": "dist", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src"], "exclude": ["node_modules", "dist"]}