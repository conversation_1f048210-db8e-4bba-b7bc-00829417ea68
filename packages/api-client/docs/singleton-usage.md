# API 客户端单例模式使用指南

本文档介绍如何使用 API 客户端的单例模式和可配置的错误通知功能。

## 基本概念

API 客户端现在使用单例模式实现，确保整个应用中只有一个 Axios 实例。这带来以下好处：

1. 统一的配置管理
2. 减少资源消耗
3. 运行时可配置的错误通知
4. 保持向后兼容性

## 使用方法

### 基本用法（向后兼容）

```typescript
import { http, httpClient } from '@yixiaoer/api-client';

// 使用默认 HTTP 客户端
const data = await http.get('/api/endpoint');

// 直接使用 Axios 实例
const response = await httpClient.get('/api/endpoint');
```

### 配置 API 客户端

```typescript
import { configureApiClient } from '@yixiaoer/api-client';
import { toast } from 'sonner'; // 或其他通知库

// 配置 API 客户端
configureApiClient(
  {
    // API 配置
    baseUrl: 'https://api.example.com',
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'X-App-Version': '1.0.0'
    }
  },
  // 自定义错误通知函数
  (title, text, status) => {
    toast.error(`${title}: ${text}`, {
      description: status ? `状态码: ${status}` : undefined
    });
    
    // 可以添加额外的错误处理逻辑
    if (status === 500) {
      console.error('服务器错误:', text);
      // 可以发送到错误监控系统
    }
  }
);
```

### 直接访问单例

```typescript
import { HttpClientSingleton } from '@yixiaoer/api-client';

// 获取单例实例
const clientSingleton = HttpClientSingleton.getInstance();

// 获取当前配置
const currentConfig = clientSingleton.apiConfig;
console.log('当前 API 基础 URL:', currentConfig.baseUrl);

// 动态修改配置
clientSingleton.configure({
  timeout: 30000, // 只修改超时时间
});

// 修改错误通知函数
clientSingleton.configure(
  undefined, // 不修改 API 配置
  (title, text, status) => {
    // 新的错误通知实现
    console.error(`[${title}] ${text}`, status);
    // 使用自定义通知组件
  }
);
```

## 在不同环境中配置

### 在 React 应用中

```typescript
// src/api/config.ts
import { configureApiClient } from '@yixiaoer/api-client';
import { toast } from 'sonner';

export function setupApiClient() {
  configureApiClient(
    {
      baseUrl: process.env.REACT_APP_API_URL || '/api',
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json',
        'X-App-Version': process.env.REACT_APP_VERSION || '1.0.0'
      }
    },
    (title, text, status) => {
      toast.error(`${title}: ${text}`);
    }
  );
}

// src/index.tsx
import { setupApiClient } from './api/config';

// 应用启动时配置 API 客户端
setupApiClient();
```

### 在 Vue 应用中

```typescript
// src/plugins/api.ts
import { configureApiClient } from '@yixiaoer/api-client';
import { useToast } from 'vue-toastification';

export function setupApiClient() {
  const toast = useToast();
  
  configureApiClient(
    {
      baseUrl: import.meta.env.VITE_API_URL || '/api',
      timeout: 15000
    },
    (title, text) => {
      toast.error(`${title}: ${text}`);
    }
  );
}

// src/main.ts
import { setupApiClient } from './plugins/api';

// 应用启动时配置 API 客户端
setupApiClient();
```

## 高级用法

### 创建自定义 HTTP 客户端

虽然单例模式确保了全局只有一个 Axios 实例，但您仍然可以使用 `createHttpClient` 函数获取配置后的客户端：

```typescript
import { createHttpClient } from '@yixiaoer/api-client';

// 创建 HTTP 客户端（实际上是配置单例并返回引用）
const { http, axiosInstance } = createHttpClient({
  baseUrl: 'https://api.other-service.com',
  timeout: 30000
});

// 使用配置后的客户端
const data = await http.get('/endpoint');
```

### 在测试中使用

在测试环境中，您可能需要为每个测试用例重置或配置不同的客户端：

```typescript
import { HttpClientSingleton, configureApiClient } from '@yixiaoer/api-client';

beforeEach(() => {
  // 重置单例配置
  configureApiClient({
    baseUrl: 'http://localhost:3000',
    timeout: 1000
  });
});

test('API 请求成功', async () => {
  // 使用配置后的客户端进行测试
  // ...
});
```

## 注意事项

1. 虽然 `createHttpClient` 函数仍然可用，但它现在只是配置单例并返回引用，而不是创建新实例
2. 所有通过 `http` 和 `httpClient` 的请求都会使用最新的配置
3. 错误通知函数可以在运行时随时更改
4. 单例模式确保了全局只有一个 Axios 实例，减少了资源消耗
