import { useEffect, useMemo, useState } from "react";
import { useSendVerifyCodeMutation } from "../hooks/user";
import { phoneRegExp } from "../constants/user";
import { LoadingButton } from "./loadingButton";
export function VerificationCodeButton({
  sence = "auth",
  onSendSuccess,
  phone,
}: {
  sence?: "auth" | "password" | "juguang";
  onSendSuccess?: (code: string) => void;
  phone: string;
}) {
  const [countdown, setCountdown] = useState(0); // 倒计时秒数
  const [isButtonDisabled, setIsButtonDisabled] = useState(false); // 按钮状态
  const mutation = useSendVerifyCodeMutation({
    onSuccess: (data) => {
      setIsButtonDisabled(true); // 禁用按钮
      setCountdown(60); // 开始 60 秒倒计时
      if (data) onSendSuccess?.(data);
      // 发送验证码成功
    },
    onError: (error) => {
      // 发送验证码失败
      console.log(error);
    },
  });

  const disabled = useMemo(
    () => !phoneRegExp.test(phone) || isButtonDisabled,
    [phone, isButtonDisabled]
  );

  // 倒计时逻辑
  useEffect(() => {
    if (countdown > 0) {
      const timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000); // 每秒更新一次
      return () => clearInterval(timer); // 清除定时器
    } else {
      setIsButtonDisabled(false); // 倒计时结束，启用按钮
    }
  }, [countdown]); // 当 countdown 变化时运行

  const requestVerificationCode = () => {
    if (isButtonDisabled) return; // 防止重复点击
    // 这里模拟发送验证码请求
    mutation.mutate({
      phone,
      sence,
    });
  };
  return (
    <LoadingButton
      onClick={requestVerificationCode}
      disabled={disabled || mutation.isPending}
      type="button"
      className="text-base text-primary"
      variant="ghost"
      isPending={mutation.isPending}
    >
      {mutation.isPending
        ? ""
        : isButtonDisabled
          ? `${countdown}s`
          : "获取验证码"}
    </LoadingButton>
  );
}
