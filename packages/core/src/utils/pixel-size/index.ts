/**
 * 像素尺寸比较工具
 */

export class PixelSize {
  private constructor(
    public width: number,
    public height: number,
  ) {}

  static from(width: number, height: number) {
    return new PixelSize(width, height)
  }

  // 任意维度大于
  someDimensionGreaterThan(size: PixelSize) {
    return this.width > size.width || this.height > size.height
  }

  // 所有维度大于
  everyDimensionGreaterThan(size: PixelSize) {
    return this.width > size.width && this.height > size.height
  }

  // 任意维度小于
  someDimensionLesserThan(size: PixelSize) {
    return this.width < size.width || this.height < size.height
  }

  // 所有维度小于
  everyDimensionLesserThan(size: PixelSize) {
    return this.width < size.width && this.height < size.height
  }

  // 等于
  equals(size: PixelSize) {
    return this.width === size.width && this.height === size.height
  }

  // 任意维度大于等于
  someDimensionGreaterThanOrEquals(size: PixelSize) {
    return this.width >= size.width || this.height >= size.height
  }

  // 所有维度大于等于
  everyDimensionGreaterThanOrEquals(size: PixelSize) {
    return this.width >= size.width && this.height >= size.height
  }

  // 任意维度小于等于
  someDimensionLesserThanOrEquals(size: PixelSize) {
    return this.width <= size.width || this.height <= size.height
  }

  // 所有维度小于等于
  everyDimensionLesserThanOrEquals(size: PixelSize) {
    return this.width <= size.width && this.height <= size.height
  }
}
