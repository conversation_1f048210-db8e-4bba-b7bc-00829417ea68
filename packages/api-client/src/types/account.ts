import type { BaseEntity, PlatformBase } from './utils';
import type { QueryParams } from './api';

/**
 * 平台
 */
export interface Platform extends PlatformBase {
  icon: string;
}

/**
 * 账号列表查询参数
 */
export interface AccountListQuery extends QueryParams {
  group?: string;
  platform?: string;
}

/**
 * 平台账号
 */
export interface PlatformAccount extends BaseEntity {
  groups: string[];
  favorites: string[];
  isFreeze: boolean;
  // 是否有账号运营权限
  isOperate: boolean;
  // 账号是否被锁定
  isLock: boolean;
  platformAccountName: string;
  platformAuthorId: string;
  platformAvatar: string;
  platformName: string;
  // 0其他平台 1开放平台
  platformType: number;
  remark: string;
  parentId?: string;
  spaceId: string;
  //账号登录状态 <0：未登录, 1：登录成功, 2：登录过期, 3：登录失败, 4：取消授权>
  status: number;
  kuaidailiArea?: string;
  isRealNameVerified: boolean;
}

/**
 * 群组
 */
export interface Groups extends BaseEntity {
  accounts: string[];
  name: string;
}
