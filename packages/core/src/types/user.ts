import { z } from "zod";
import { phoneRegExp, verifyCodeRegExp } from "../constants/user";

export const loginSchema = z.object({
  phone: z
    .string()
    .min(1, { message: "手机号码不能为空" })
    .regex(phoneRegExp, { message: "请输入正确的手机号码" }),
  code: z
    .string()
    .min(1, { message: "验证码不能为空" })
    .regex(verifyCodeRegExp, { message: "验证码格式不正确" }),
});
export type LoginFormInputs = z.infer<typeof loginSchema>;
