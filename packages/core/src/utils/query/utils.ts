import {
  useMutation,
  UseMutationOptions,
  useQuery,
  UseQueryOptions,
  QueryKey,
  QueryFunction,
} from "@tanstack/react-query";

/**
 * 工厂方法：创建一个 mutation hook
 *
 * 该工厂方法接收一个 mutationFn 参数，返回一个可复用的 hook 函数。
 * 返回的 hook 函数接收除 mutationFn 之外的所有 UseMutationOptions 参数。
 *
 * @template TData - mutation 返回的数据类型
 * @template TError - 错误类型，默认为 Error
 * @template TVariables - mutation 函数的参数类型，默认为 void
 * @template TContext - mutation 上下文类型，默认为 unknown
 *
 * @param mutationFn - mutation 函数，接收 TVariables 类型参数，返回 Promise<TData>
 * @returns 返回一个 hook 函数，该函数接收 UseMutationOptions（排除 mutationFn）
 */
export function createMutationHook<
  TData,
  TError = Error,
  TVariables = void,
  TContext = unknown,
>(mutationFn: (variables: TVariables) => Promise<TData>) {
  return function useMutationHook(
    options?: Omit<
      UseMutationOptions<TData, TError, TVariables, TContext>,
      "mutationFn"
    >
  ) {
    return useMutation<TData, TError, TVariables, TContext>({
      mutationFn,
      ...options,
    });
  };
}

/**
 * 工厂方法：创建一个 query hook
 *
 * 该工厂方法接收一个 queryFn 参数和 queryKey，返回一个可复用的 hook 函数。
 * 返回的 hook 函数接收除 queryFn 和 queryKey 之外的所有 UseQueryOptions 参数。
 *
 * @template TQueryFnData - query 函数返回的数据类型
 * @template TError - 错误类型，默认为 Error
 * @template TData - 最终返回的数据类型，默认与 TQueryFnData 相同
 * @template TQueryKey - query key 的类型，默认为 QueryKey
 *
 * @param queryKey - query key，用于缓存和标识查询
 * @param queryFn - query 函数，返回 Promise<TQueryFnData>
 * @returns 返回一个 hook 函数，该函数接收 UseQueryOptions（排除 queryKey 和 queryFn）
 */
export function createQueryHook<
  TQueryFnData = unknown,
  TError = Error,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(queryKey: TQueryKey, queryFn: QueryFunction<TQueryFnData, TQueryKey>) {
  return function useQueryHook(
    options?: Omit<
      UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,
      "queryKey" | "queryFn"
    >
  ) {
    return useQuery<TQueryFnData, TError, TData, TQueryKey>({
      queryKey,
      queryFn,
      ...options,
    });
  };
}

export { useQueryClient } from "@tanstack/react-query";
