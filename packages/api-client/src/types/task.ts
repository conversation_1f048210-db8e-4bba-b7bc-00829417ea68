import type { BaseEntity, Status } from './utils';

/**
 * 任务状态类型
 */
export type TaskDeliveryStatus = Extract<Status, 'pending' | 'delivering' | 'successful' | 'failed'>;
export type TaskAuditStatus = Extract<Status, 'auditing' | 'rejected' | 'approved'>;
export type TaskSetStatus = 'publishing' | 'allsuccessful' | 'partialsuccessful' | 'allfailed';

export type ContentType = 'imageText' | 'video' | 'article' | 'gongzhonghao' | 'dynamic';

/**
 * 任务集
 */
export interface TaskWrapper extends BaseEntity {
  /**
   * 平均任务耗时(秒)
   */
  avTaskDuration: number;
  /**
   * 封面
   */
  coverKey?: string;
  /**
   * 封面
   */
  coverUrl?: string;
  /**
   * 描述
   */
  desc?: string;
  /**
   * 结构化描述信息（可以是富文本）
   */
  descRich?: Record<string, unknown>;
  /**
   * 失败数
   */
  failedCount: number;
  isAppContent: boolean;
  /**
   * 是否是草稿
   */
  isDraft: boolean;
  /**
   * 是否定时任务
   */
  isTimed: number;
  nickName: string;
  /**
   * 发布中数
   */
  penddingCount: number;
  /**
   * 账号媒体
   */
  platforms: string[];
  /**
   * 编辑类型
   */
  publishType: ContentType;
  successCount: number;
  /**
   * 任务数
   */
  taskCount: number;
  /**
   * 任务集状态，任务集状态: publishing(发布中), allsuccessful(全部发布成功), partialsuccessful(部分发布成功),
   * allfailed(全部发布失败)
   */
  taskSetStatus: TaskSetStatus;
  /**
   * 任务总耗时(秒)
   */
  totalTaskDuration: number;
  userId: string;
}

/**
 * 任务
 */
export interface Task {
  errorMessage: string;
  stageStatus: any | null; // 为null是服务端没有被上报状态情况下的初始值
  stages: any | null; // 为null是服务端没有被上报状态情况下的初始值
  auditFailedMessage: string;
  auditStatus: string;
  cover: string;
  createdAt: string;
  deliveryFailedMessage: string;
  deliveryStatus: string;
  desc: string;
  documentId: string;
  id: string;
  isAppContent: boolean;
  nickName: string;
  phone: string;
  platformAccountId: string;
  platformAccountName: string;
  platformAvatar: string;
  platformName: string;
  publishId: string | null;
  publishType: string;
  mediaType?: string; // 为空是历史数据
  reviewStatus: string;
  taskId: string;
  taskStatusCode: number;
  auditTime: number;
  deliveryTime: number;
  statistic?: any;
}

export interface PushingTaskSetResponse {
  isTimed: number;
  /**
   * 平均任务耗时(秒)
   */
  avTaskDuration: number;
  /**
   * 封面
   */
  coverKey?: string;
  /**
   * 封面
   */
  coverUrl?: string;
  /**
   * 任务集创建时间
   */
  createdAt: number;
  /**
   * 描述
   */
  desc?: string;
  /**
   * 结构化描述信息（可以是富文本）
   */
  descRich?: { [key: string]: unknown };
  /**
   * 失败数
   */
  failedCount: number;
  /**
   * 是否是草稿
   */
  isDraft: boolean;
  /**
   * 任务集的taskIdentityId
   */
  id: string;
  isAppContent: boolean;
  nickName: string;
  /**
   * 发布中数
   */
  penddingCount: number;
  /**
   * 账号媒体
   */
  platforms: string[];
  /**
   * 发布类型
   */
  publishType: string;
  /**
   * 成功数
   */
  successCount: number;
  /**
   * 任务数
   */
  taskCount: number;
  /**
   * 任务集状态，任务集状态: publishing(发布中), allsuccessful(全部发布成功), partialsuccessful(部分发布成功),
   * allfailed(全部发布失败)
   */
  taskSetStatus: TaskSetStatus;
  /**
   * 任务总耗时(秒)
   */
  totalTaskDuration: number;
  userId: string;
  publishChannel: 'local' | 'cloud';
}

/**
 * 创建任务请求体类型
 */
export interface CreateTaskRequest {
  coverKey?: string;
  desc?: string;
  isDraft?: boolean;
  isTimed?: number; // 示例是时间戳
  platformAccounts: {
    videoKey: string;
    coverKey: string;
    platformAccountId: string;
  }[];
  platforms: string[]; // e.g., ["抖音", "快手"]
  publishType: string; // e.g., "video"
  publishChannel: 'local' | 'cloud'; // e.g., "cloud"
  publishArgs: Record<string, unknown>;
  isAppContent?: boolean;
}
