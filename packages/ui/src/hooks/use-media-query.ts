import { useEffect, useState } from 'react';

/**
 * 媒体查询Hook
 * 用于响应式设计，检测当前视口是否匹配指定的媒体查询
 * 
 * @param query 媒体查询字符串
 * @returns 是否匹配
 * 
 * @example
 * ```tsx
 * const isMobile = useMediaQuery('(max-width: 768px)');
 * ```
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);
    
    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
}
